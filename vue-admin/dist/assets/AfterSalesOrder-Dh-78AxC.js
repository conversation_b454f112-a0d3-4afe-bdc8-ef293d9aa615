import{_ as U,r as h,w as B,a as E,b as F,d as o,z as t,A as l,B as i,h as G,f as c,I as x,J as M,K,t as n,L as k}from"./index-DDnhcbb3.js";const L={class:"page-container"},R={class:"search-section"},j={class:"layui-card main-card"},H={class:"layui-card-body"},Q={class:"list_search"},W={class:"table-section"},X={class:"layui-card main-card table-card"},Y={class:"layui-card-body"},Z={class:"table-wrapper"},ee={class:"after-sales-info"},te={class:"store-info flex items-center"},ae=["src"],oe={class:"store-badge mx-2"},le={class:"order-info"},se={class:"info-item"},ne={class:"value"},ie=["title"],de={class:"text-xs text-gray-500 mt-1"},ue={class:"ml-2"},re={class:"text-sm"},ce={class:"font-semibold"},_e={class:"font-semibold text-red-600"},pe={class:"text-xs text-gray-500 mt-1"},we={class:"text-sm font-medium"},me={class:"text-sm text-gray-500"},he={class:"action-buttons"},fe={class:"pagination-wrapper"},ge={__name:"AfterSalesOrder",setup(be){const d=h({productName:"",afterSalesType:"",status:""}),w=h([]),r=h(1),_=h(10),m=h(0),N=()=>{console.log("搜索:",d.value),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单搜索按钮",text:JSON.stringify(d.value)})}),k.success("搜索功能待实现")},z=()=>{d.value={productName:"",afterSalesType:"",status:""},window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单重置按钮"})})},C=(s,a)=>{console.log("忽略售后工单:",s,a),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单忽略",key:a.orderNumber,data:a})}),k.success("已忽略该售后工单")},V=(s,a)=>{console.log("查看售后工单详情:",s,a),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单查看详情",key:a.orderNumber,data:a})})},I=s=>{_.value=s,console.log(`每页 ${s} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单分页大小改变",pageSize:s})})},O=s=>{r.value=s,console.log(`当前页: ${s}`),window.g_click&&window.g_click({request:JSON.stringify({action:"售后工单分页按钮",page:s})})},b=()=>{window.table_shouhou=w.value,window.table_shouhou_currentPage=r.value,window.table_shouhou_pageSize=_.value,window.table_shouhou_total=m.value};B([w,r,_,m],()=>{b()},{deep:!0});const v=()=>{window.table_shouhou&&Array.isArray(window.table_shouhou)&&(w.value=window.table_shouhou),window.table_shouhou_currentPage&&(r.value=window.table_shouhou_currentPage),window.table_shouhou_pageSize&&(_.value=window.table_shouhou_pageSize),window.table_shouhou_total&&(m.value=window.table_shouhou_total)},J=()=>{w.value=[],m.value=0,r.value=1};return window.updateAfterSalesData=v,E(()=>{window.table_shouhou_currentPage=1,window.table_shouhou_pageSize=2,window.table_shouhou_total=12926,window.table_shouhou&&window.table_shouhou.length>0?v():(J(),b())}),(s,a)=>{const q=i("el-input"),f=i("el-col"),p=i("el-option"),y=i("el-select"),S=i("el-icon"),g=i("el-button"),A=i("el-row"),u=i("el-table-column"),D=i("el-tag"),P=i("el-table"),T=i("el-pagination");return G(),F("div",L,[o("div",R,[o("div",j,[a[7]||(a[7]=o("div",{class:"layui-card-header"},"售后工单",-1)),o("div",H,[o("div",Q,[t(A,{gutter:20},{default:l(()=>[t(f,{span:6},{default:l(()=>[t(q,{modelValue:d.value.productName,"onUpdate:modelValue":a[0]||(a[0]=e=>d.value.productName=e),placeholder:"商品名称",clearable:""},null,8,["modelValue"])]),_:1}),t(f,{span:6},{default:l(()=>[t(y,{modelValue:d.value.afterSalesType,"onUpdate:modelValue":a[1]||(a[1]=e=>d.value.afterSalesType=e),placeholder:"售后类型",clearable:"",style:{width:"100%"}},{default:l(()=>[t(p,{label:"商品不满意",value:"unsatisfied"}),t(p,{label:"质量问题",value:"quality"}),t(p,{label:"商品损坏",value:"damage"})]),_:1},8,["modelValue"])]),_:1}),t(f,{span:6},{default:l(()=>[t(y,{modelValue:d.value.status,"onUpdate:modelValue":a[2]||(a[2]=e=>d.value.status=e),placeholder:"售后状态",clearable:"",style:{width:"100%"}},{default:l(()=>[t(p,{label:"待处理",value:"pending"}),t(p,{label:"处理中",value:"processing"}),t(p,{label:"已完成",value:"completed"})]),_:1},8,["modelValue"])]),_:1}),t(f,{span:6},{default:l(()=>[t(g,{type:"primary",onClick:N,class:"btn2"},{default:l(()=>[t(S,null,{default:l(()=>[t(x(M))]),_:1}),a[5]||(a[5]=c(" 搜索 "))]),_:1,__:[5]}),t(g,{onClick:z,class:"btn6"},{default:l(()=>[t(S,null,{default:l(()=>[t(x(K))]),_:1}),a[6]||(a[6]=c(" 重置 "))]),_:1,__:[6]})]),_:1})]),_:1})])])])]),o("div",W,[o("div",X,[o("div",Y,[o("div",Z,[t(P,{data:w.value,style:{width:"100%"},stripe:"",border:"",class:"stable-table"},{default:l(()=>[t(u,{label:"售后信息","min-width":"200"},{default:l(e=>[o("div",ee,[o("div",te,[o("img",{src:e.row.platformIcon,alt:"平台",class:"w-6 h-6 mr-2"},null,8,ae),o("span",oe,n(e.row.storeName),1)]),o("div",le,[o("div",se,[o("span",ne,n(e.row.afterSalesNumber),1)])])])]),_:1}),t(u,{label:"商品信息","min-width":"300"},{default:l(e=>[o("p",{class:"text-sm font-medium line-clamp-2",title:e.row.productInfo.name},n(e.row.productInfo.name),9,ie),o("div",de,[o("span",null,"ID："+n(e.row.productInfo.id),1),o("span",ue,"规格："+n(e.row.productInfo.spec),1)])]),_:1}),t(u,{label:"金额","min-width":"180"},{default:l(e=>[o("div",re,[o("div",null,[a[8]||(a[8]=c("付款金额：")),o("span",ce,"¥"+n(e.row.amount.paid),1)]),o("div",null,[a[9]||(a[9]=c("售后金额：")),o("span",_e,"¥"+n(e.row.amount.afterSales),1)])])]),_:1}),t(u,{label:"售后类型","min-width":"120"},{default:l(e=>[t(D,{color:e.row.typeColor,size:"small",style:{color:"white",border:"none"}},{default:l(()=>[c(n(e.row.type),1)]),_:2},1032,["color"]),o("p",pe,n(e.row.status),1)]),_:1}),t(u,{prop:"reason",label:"申请原因","min-width":"200","show-overflow-tooltip":""}),t(u,{label:"快递信息","min-width":"200"},{default:l(e=>[o("p",we,n(e.row.logistics.company)+" "+n(e.row.logistics.number),1),o("p",me,n(e.row.logistics.status),1)]),_:1}),t(u,{prop:"applyTime",label:"售后申请时间","min-width":"180"}),t(u,{label:"操作",width:"120",fixed:"right"},{default:l(e=>[o("div",he,[t(g,{size:"small",type:"primary",onClick:$=>C(e.$index,e.row),class:"btn2 action-btn"},{default:l(()=>a[10]||(a[10]=[c(" 忽略 ")])),_:2,__:[10]},1032,["onClick"]),t(g,{size:"small",type:"danger",onClick:$=>V(e.$index,e.row),class:"btn4 action-btn"},{default:l(()=>a[11]||(a[11]=[c(" 查看详情 ")])),_:2,__:[11]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])]),o("div",fe,[t(T,{"current-page":r.value,"onUpdate:currentPage":a[3]||(a[3]=e=>r.value=e),"page-size":_.value,"onUpdate:pageSize":a[4]||(a[4]=e=>_.value=e),"page-sizes":[10,20,50,100],total:m.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:I,onCurrentChange:O,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])])])}}},ye=U(ge,[["__scopeId","data-v-bad52c96"]]);export{ye as default};
