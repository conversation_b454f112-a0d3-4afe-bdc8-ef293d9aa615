import{_ as q,r as v,i as H,c as J,p as j,j as K,k as W,l as X,m as Y,q as Q,s as Z,v as ee,a as O,o as te,x as P,b as S,y as N,z as e,A as o,F as L,g as ne,B as u,C as ae,d as m,D as le,t as D,E as oe,G as ie,T as se,n as ce,h,H as ue,w as de,f as R,I as U,J as re,K as _e,L as pe}from"./index-DDnhcbb3.js";const me={key:0,class:"context-menu-divider"},fe={key:1,class:"context-menu-divider"},we={__name:"ContextMenu",props:{customMenuItems:{type:Array,default:()=>[]}},emits:["menu-click"],setup($,{expose:_,emit:x}){const f=$,b=x,r=v(!1),w=v(null),M=v(null),i=H({x:0,y:0});let p=null;const A=[{action:"子账号管理_添加子账号",label:"添加子账号",icon:j,type:"add"},{action:"子账号管理_删除子账号",label:"删除子账号",icon:K,type:"danger"},{action:"子账号管理_充值点数",label:"充值点数",icon:W,type:"normal"},{action:"子账号管理_回收点数",label:"回收点数",icon:X,type:"normal"},{action:"子账号管理_禁用账户",label:"禁用账户",icon:Y,type:"normal"},{action:"子账号管理_复制账号密码",label:"复制账号密码",icon:Q,type:"normal"},{action:"子账号管理_修改登陆密码",label:"修改登陆密码",icon:Z,type:"normal"},{action:"子账号管理_设置账号等级",label:"设置账号等级",icon:ee,type:"normal"}],B=J(()=>f.customMenuItems.length>0?f.customMenuItems:A),E=J(()=>({position:"fixed",left:`${i.x}px`,top:`${i.y}px`,zIndex:9999})),V=(l,t,g)=>{p&&clearTimeout(p),r.value&&y(),p=setTimeout(()=>{M.value=g,i.x=l,i.y=t,r.value=!0,ce(()=>{I()})},10)},y=()=>{p&&(clearTimeout(p),p=null),r.value=!1,M.value=null},I=()=>{if(!w.value)return;const t=w.value.getBoundingClientRect(),g=window.innerWidth,C=window.innerHeight,c=10;if(i.x+t.width>g-c){const n=i.x-t.width;n>=c?i.x=n:i.x=g-t.width-c}if(i.y+t.height>C-c){const n=i.y-t.height;n>=c?i.y=n:i.y=C-t.height-c}i.x<c&&(i.x=c),i.y<c&&(i.y=c)},d=async l=>{const t=M.value;if(y(),l==="子账号管理_删除子账号")try{await ue.confirm(`确定要删除账号 ${t?.account} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),a(l,t)}catch{}else a(l,t)},a=(l,t)=>{window.g_click&&window.g_click({request:JSON.stringify({action:l,account:t?.account,data:t})}),b("menu-click",{action:l,data:t})},z=l=>{r.value&&w.value&&!w.value.contains(l.target)&&y()},k=l=>{l.key==="Escape"&&r.value&&y()};return O(()=>{document.addEventListener("click",z),document.addEventListener("keydown",k)}),te(()=>{document.removeEventListener("click",z),document.removeEventListener("keydown",k),p&&(clearTimeout(p),p=null)}),_({show:V,hide:y}),(l,t)=>{const g=u("el-icon"),C=u("el-menu-item"),c=u("el-menu");return h(),P(se,{to:"body"},[r.value?(h(),S("div",{key:0,ref_key:"menuRef",ref:w,class:"context-menu",style:ie(E.value),onClick:t[0]||(t[0]=oe(()=>{},["stop"]))},[e(c,{class:"context-menu-list",onSelect:d},{default:o(()=>[(h(!0),S(L,null,ne(B.value,(n,T)=>(h(),S(L,{key:n.action},[T===1?(h(),S("div",me)):N("",!0),e(C,{index:n.action,class:ae(n.type)},{default:o(()=>[n.icon?(h(),P(g,{key:0},{default:o(()=>[(h(),P(le(n.icon)))]),_:2},1024)):N("",!0),m("span",null,D(n.label),1)]),_:2},1032,["index","class"]),T===3?(h(),S("div",fe)):N("",!0)],64))),128))]),_:1})],4)):N("",!0)])}}},ge=q(we,[["__scopeId","data-v-c24b576c"]]),he={class:"page-container"},ve={class:"search-section"},be={class:"layui-card main-card"},ye={class:"layui-card-body"},ze={class:"list_search"},xe={class:"table-section"},ke={class:"layui-card main-card table-card"},Ce={class:"layui-card-body"},Se={class:"table-wrapper p-4"},Me={class:"pagination-wrapper"},Te={__name:"SubAccountManage",setup($){const _=v({account:"",status:"",online:""}),x=v([]),f=v(1),b=v(10),r=v(0),w=v(null),M=()=>{console.log("搜索:",_.value),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理搜索按钮",text:JSON.stringify(_.value)})}),pe.success("搜索功能待实现")},i=()=>{_.value={account:"",status:"",online:""},window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理重置按钮"})})},p=d=>{b.value=d,console.log(`每页 ${d} 条`),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理分页大小改变",pageSize:d})})},A=d=>{f.value=d,console.log(`当前页: ${d}`),window.g_click&&window.g_click({request:JSON.stringify({action:"子账号管理分页按钮",page:d})})},B=(d,a,z)=>{z.preventDefault(),w.value&&w.value.show(z.clientX,z.clientY,d)},E=({action:d,data:a})=>{},V=()=>{window.table_zizhanghao=x.value,window.table_zizhanghao_currentPage=f.value,window.table_zizhanghao_pageSize=b.value,window.table_zizhanghao_total=r.value};de([x,f,b,r],()=>{V()},{deep:!0});const y=()=>{window.table_zizhanghao&&Array.isArray(window.table_zizhanghao)&&(x.value=window.table_zizhanghao),window.table_zizhanghao_currentPage&&(f.value=window.table_zizhanghao_currentPage),window.table_zizhanghao_pageSize&&(b.value=window.table_zizhanghao_pageSize),window.table_zizhanghao_total&&(r.value=window.table_zizhanghao_total)},I=()=>{x.value=[],r.value=0,f.value=1};return window.updateSubAccountData=y,O(()=>{window.table_zizhanghao&&window.table_zizhanghao.length>0?y():(I(),V())}),(d,a)=>{const z=u("el-input"),k=u("el-col"),l=u("el-option"),t=u("el-select"),g=u("el-icon"),C=u("el-button"),c=u("el-row"),n=u("el-table-column"),T=u("el-tag"),F=u("el-table"),G=u("el-pagination");return h(),S("div",he,[m("div",ve,[m("div",be,[a[7]||(a[7]=m("div",{class:"layui-card-header"},"子账号管理",-1)),m("div",ye,[m("div",ze,[e(c,{gutter:20},{default:o(()=>[e(k,{span:6},{default:o(()=>[e(z,{modelValue:_.value.account,"onUpdate:modelValue":a[0]||(a[0]=s=>_.value.account=s),placeholder:"搜索账号",clearable:""},null,8,["modelValue"])]),_:1}),e(k,{span:6},{default:o(()=>[e(t,{modelValue:_.value.status,"onUpdate:modelValue":a[1]||(a[1]=s=>_.value.status=s),placeholder:"账号状态",clearable:"",style:{width:"100%"}},{default:o(()=>[e(l,{label:"正常",value:"normal"}),e(l,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1}),e(k,{span:6},{default:o(()=>[e(t,{modelValue:_.value.online,"onUpdate:modelValue":a[2]||(a[2]=s=>_.value.online=s),placeholder:"是否在线",clearable:"",style:{width:"100%"}},{default:o(()=>[e(l,{label:"在线",value:"online"}),e(l,{label:"离线",value:"offline"})]),_:1},8,["modelValue"])]),_:1}),e(k,{span:6},{default:o(()=>[e(C,{type:"primary",onClick:M,class:"btn2"},{default:o(()=>[e(g,null,{default:o(()=>[e(U(re))]),_:1}),a[5]||(a[5]=R(" 搜索 "))]),_:1,__:[5]}),e(C,{onClick:i,class:"btn6"},{default:o(()=>[e(g,null,{default:o(()=>[e(U(_e))]),_:1}),a[6]||(a[6]=R(" 重置 "))]),_:1,__:[6]})]),_:1})]),_:1})])])])]),m("div",xe,[m("div",ke,[m("div",Ce,[m("div",Se,[e(F,{data:x.value,style:{width:"100%"},stripe:"",border:"",class:"stable-table",onRowContextmenu:B},{default:o(()=>[e(n,{prop:"account",label:"账号","min-width":"80"}),e(n,{prop:"level",label:"等级","min-width":"60"}),e(n,{prop:"balance",label:"余额","min-width":"70"}),e(n,{prop:"expireTime",label:"到期时间","min-width":"100"}),e(n,{prop:"status",label:"状态","min-width":"60"},{default:o(s=>[e(T,{type:s.row.status==="正常"?"success":"danger",size:"small"},{default:o(()=>[R(D(s.row.status),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"loginTime",label:"登陆时间","min-width":"100"}),e(n,{prop:"online",label:"在线","min-width":"50"},{default:o(s=>[e(T,{type:s.row.online==="在线"?"success":"info",size:"small"},{default:o(()=>[R(D(s.row.online),1)]),_:2},1032,["type"])]),_:1}),e(n,{prop:"storeCount",label:"店铺","min-width":"50"}),e(n,{prop:"onlineStoreCount",label:"在线店铺","min-width":"70"})]),_:1},8,["data"])]),m("div",Me,[e(G,{"current-page":f.value,"onUpdate:currentPage":a[3]||(a[3]=s=>f.value=s),"page-size":b.value,"onUpdate:pageSize":a[4]||(a[4]=s=>b.value=s),"page-sizes":[10,20,50,100],total:r.value,layout:"prev, pager, next","prev-text":"上一页","next-text":"下一页",onSizeChange:p,onCurrentChange:A,class:"layui-pagination"},null,8,["current-page","page-size","total"])])])])]),e(ge,{ref_key:"contextMenuRef",ref:w,onMenuClick:E},null,512)])}}},Ne=q(Te,[["__scopeId","data-v-614e0fc1"]]);export{Ne as default};
