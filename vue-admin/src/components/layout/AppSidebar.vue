<template>
  <div class="sidebar" :class="{ 'collapsed': sidebarCollapsed }" id="sidebar">
    <div class="menu-container">
      <router-link to="/dashboard" class="menu-item" :class="{ 'active': $route.name === 'Dashboard' }" @click="handleMenuClick('仪表盘', '/dashboard')">
        <i class="iconfont">&#xe6d2;</i>
        <span>仪表盘</span>
      </router-link>
      <router-link to="/sub-account" class="menu-item" :class="{ 'active': $route.name === 'SubAccountManage' }" @click="handleMenuClick('子账号管理', '/sub-account')">
        <i class="iconfont">&#xe612;</i>
        <span>子账号管理</span>
      </router-link>
      <router-link to="/store" class="menu-item" :class="{ 'active': $route.name === 'StoreManage' }" @click="handleMenuClick('店铺管理', '/store')">
        <i class="iconfont">&#xe6a2;</i>
        <span>店铺管理</span>
      </router-link>
      <router-link to="/work-order" class="menu-item" :class="{ 'active': $route.name === 'WorkOrderPanel' }" @click="handleMenuClick('工单面板', '/work-order')">
        <i class="iconfont">&#xe685;</i>
        <span>对话预警</span>
      </router-link>
      <router-link to="/after-sales" class="menu-item" :class="{ 'active': $route.name === 'AfterSalesOrder' }" @click="handleMenuClick('售后工单', '/after-sales')">
        <i class="iconfont">&#xe685;</i>
        <span>售后面板</span>
      </router-link>
      <div class="line"></div>
      <div class="color1" style="margin-bottom: 20px">消息</div>
      <div class="menu-item" @click="handleMessageClick('收件箱')">
        <i class="iconfont">&#xe629;</i>
        <span>收件箱</span>
        <div class="message-count">{{ messageCount.inbox }}</div>
      </div>
      <div class="menu-item" @click="handleMessageClick('通知')">
        <i class="iconfont">&#xe60a;</i>
        <span>通知</span>
        <div class="message-count">{{ messageCount.notification }}</div>
      </div>
    </div>
    <div class="user-info">
      <div class="user-avatar">{{ userInfo.avatar }}</div>
      <div class="user-details">
        <div class="user-name">{{ userInfo.name }}</div>
        <div class="user-position">{{ userInfo.position }}</div>
      </div>
    </div>
    <div class="text3">
      <span>点数余额：</span>
      <span>{{ userInfo.balance }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, watch } from 'vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

// 计算属性
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)

// 用户信息数据
const userInfo = ref({
  avatar: '张',           // 用户头像（显示的字符）
  name: '张三',           // 用户名称
  position: '管理员',      // 用户职位
  balance: '0.00'        // 点数余额
})

// 消息数量数据
const messageCount = ref({
  inbox: 0,              // 收件箱消息数量
  notification: 0        // 通知消息数量
})

// 菜单点击事件
const handleMenuClick = (menuName, path) => {
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "菜单点击",
        menu: menuName,
        path: path
      })
    })
  }
}

// 消息菜单点击事件
const handleMessageClick = (messageType) => {
  if (window.g_click) {
    window.g_click({
      request: JSON.stringify({
        action: "消息菜单点击",
        type: messageType
      })
    })
  }
}

// 设置全局变量
const setupGlobalVariables = () => {
  // 设置用户信息到全局变量
  window.sidebar_userInfo = userInfo.value
  // 设置消息数量到全局变量
  window.sidebar_messageCount = messageCount.value
}

// 从全局变量更新数据
const updateFromGlobalData = () => {
  if (window.sidebar_userInfo) {
    userInfo.value = { ...window.sidebar_userInfo }
  }
  if (window.sidebar_messageCount) {
    messageCount.value = { ...window.sidebar_messageCount }
  }
}

// 监听数据变化并更新全局变量
watch([userInfo, messageCount], () => {
  setupGlobalVariables()
}, { deep: true })

// 暴露更新函数到全局
window.updateSidebarUserInfo = updateFromGlobalData

onMounted(() => {
  // 初始化全局变量
  setupGlobalVariables()
})
</script>

<style scoped>
/* 侧边栏样式已在 old-styles.css 中定义 */
</style>
