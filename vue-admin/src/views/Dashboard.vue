<template>
  <div class="body">
    <div class="layui-row layui-col-space10">
      <div class="layui-col-md4">
        <div class="layui-card main-card dashboard-card">
          <div class="layui-card-body">
            <div class="layui-row layui-col-space15">
              <div class="layui-col-md6">
                <div class="ybp_card v1">
                  <div class="flex-0">
                    <div class="icon">
                      <i class="iconfont">&#xe612;</i>
                    </div>
                  </div>
                  <div class="w-100">
                    <p class="text1">在线账号</p>
                    <p class="text2">{{ statsData.subAccountCount }}</p>
                  </div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="ybp_card v2">
                  <div class="flex-0">
                    <div class="icon">
                      <i class="iconfont" style="color: #11cea2">&#xe6a2;</i>
                    </div>
                  </div>
                  <div class="w-100">
                    <p class="text1">在线店铺</p>
                    <p class="text2">{{ statsData.storeCount }}</p>
                  </div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="ybp_card v3">
                  <div class="flex-0">
                    <div class="icon">
                      <i class="iconfont" style="color: #3468e5">&#xe60a;</i>
                    </div>
                  </div>
                  <div class="w-100">
                    <p class="text1">今日通知次数</p>
                    <p class="text2">{{ statsData.todayNotifications }}</p>
                  </div>
                </div>
              </div>
              <div class="layui-col-md6">
                <div class="ybp_card v4">
                  <div class="flex-0">
                    <div class="icon">
                      <i class="iconfont" style="color: #f5840a">&#xe6b7;</i>
                    </div>
                  </div>
                  <div class="w-100">
                    <p class="text1">预警/售后</p>
                    <p class="text2"><span style="color: #e76537">{{ statsData.problemCount.current }}</span> / {{ statsData.problemCount.total }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="layui-col-md5">
        <div class="layui-card main-card dashboard-card">
          <div class="layui-card-header">问题分析表</div>
          <div class="layui-card-body">
            <div id="main1" style="height: 240px"></div>
          </div>
        </div>
      </div>
      <div class="layui-col-md3">
        <div class="layui-card main-card dashboard-card">
          <div class="layui-card-header">用量TOP3</div>
          <div class="layui-card-body">
            <ul class="ybp_list">
              <li class="item" v-for="(item, index) in topUsageData" :key="index">
                <div class="flex-0 text1">
                  {{ index + 1 }}
                </div>
                <div class="flex-0">
                  <img class="head" :src="item.avatar">
                </div>
                <div class="w-100">
                  <p>{{ item.name }}</p>
                  <p>用量：{{ item.usage }}</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="layui-row layui-col-space10">
      <div class="layui-col-md4">
        <div class="layui-card main-card second-row-card">
          <div class="layui-card-body">
            <div class="layui-card-header">近8小时用量分析</div>
            <div id="main2" style="height: 260px"></div>
          </div>
        </div>
      </div>
      <div class="layui-col-md8">
        <div class="layui-card main-card second-row-card">
          <div class="layui-card-header">最近消息</div>
          <div class="layui-card-body" style="height: 260px; padding: 0;">
            <div class="message-table-container">
              <table class="layui-table qxjc_t4 table-fixed">
                <colgroup>
                  <col style="width: 100px" />
                  <col style="width: 100px" />
                  <col />
                  <col />
                  <col style="width: 160px" />
                </colgroup>
                <thead>
                  <tr>
                    <th>店铺</th>
                    <th>对话人</th>
                    <th>内容</th>
                    <th>回复</th>
                    <th>时间</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(message, index) in recentMessages" :key="index">
                    <td class="layui-elip">{{ message.store }}</td>
                    <td class="layui-elip">{{ message.user }}</td>
                    <td class="layui-elip" :title="message.content">{{ message.content }}</td>
                    <td class="layui-elip">{{ message.reply }}</td>
                    <td class="layui-elip">{{ message.time }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useCharts, chartConfigs } from '@/composables/useCharts'

// 获取默认统计数据（用于调试）
const getDefaultStatsData = () => ({
  subAccountCount: 524,      // 子账号管理
  storeCount: 524,          // 店铺数量
  todayNotifications: 524,   // 今日通知次数
  problemCount: { current: 99, total: 120 }  // 问题数量
})

// 统计卡片数据（初始为空）
const statsData = ref({
  subAccountCount: 0,
  storeCount: 0,
  todayNotifications: 0,
  problemCount: { current: 0, total: 0 }
})

// 获取默认TOP3数据（用于调试）
const getDefaultTopUsageData = () => [
  { name: '是是是', usage: 123, avatar: '/img/logo.jpg' },
  { name: '是是是', usage: 123, avatar: '/img/logo.jpg' },
  { name: '是是是', usage: 123, avatar: '/img/logo.jpg' }
]

// 用量TOP3数据（初始为空）
const topUsageData = ref([])

// 获取默认消息数据（用于调试）
const getDefaultRecentMessages = () => [
  {
    store: '无忧',
    user: '是是是',
    content: '我买的裤子质里很好我买的裤子质里很好!我买的裤子质里很好!我买的裤子质里很好!我买的裤子质里很好!我买的裤子质里很好!我买的裤子质里很好!',
    reply: '亲亲这款是标准长裤哦',
    time: '2025/6/17 17:36:33'
  },
  {
    store: '无忧',
    user: '是是是',
    content: '我买的裤子质里很好!',
    reply: '亲亲这款是标准长裤哦',
    time: '2025/6/17 17:36:33'
  },
  {
    store: '无忧',
    user: '是是是',
    content: '我买的裤子质里很好!',
    reply: '亲亲这款是标准长裤哦',
    time: '2025/6/17 17:36:33'
  },
  {
    store: '无忧',
    user: '是是是',
    content: '我买的裤子质里很好!',
    reply: '亲亲这款是标准长裤哦',
    time: '2025/6/17 17:36:33'
  },
  {
    store: '无忧',
    user: '是是是',
    content: '我买的裤子质里很好!',
    reply: '亲亲这款是标准长裤哦',
    time: '2025/6/17 17:36:33'
  },
  {
    store: '无忧',
    user: '是是是',
    content: '我买的裤子质里很好!',
    reply: '亲亲这款是标准长裤哦',
    time: '2025/6/17 17:36:33'
  },
  {
    store: '无忧',
    user: '是是是',
    content: '我买的裤子质里很好!',
    reply: '亲亲这款是标准长裤哦',
    time: '2025/6/17 17:36:33'
  },
  {
    store: '无忧',
    user: '是是是',
    content: '我买的裤子质里很好!',
    reply: '亲亲这款是标准长裤哦',
    time: '2025/6/17 17:36:33'
  },
  {
    store: '无忧',
    user: '是是是',
    content: '我买的裤子质里很好!',
    reply: '亲亲这款是标准长裤哦',
    time: '2025/6/17 17:36:33'
  },
  {
    store: '无忧',
    user: '是是是',
    content: '我买的裤子质里很好!',
    reply: '亲亲这款是标准长裤哦',
    time: '2025/6/17 17:36:33'
  }
]

// 最近消息数据（初始为空）
const recentMessages = ref([])

// 获取默认图表数据（用于调试）
const getDefaultChartData = () => ({
  problemChart: {
    categories: ['昨日', '今日'],
    totalData: [320, 332],    // 总计数据
    eventData: [220, 182],    // 事件名称数据
    data: [
      {
        name: '昨日',
        eventTotal: 350,  // 事件总数
        topEventName: '订单咨询',  // top1事件名称
        topEventCount: 150  // top1事件数量
      },
      {
        name: '今日',
        eventTotal: 420,  // 事件总数
        topEventName: '售后服务',  // top1事件名称
        topEventCount: 180  // top1事件数量
      }
    ]
  },
  usageChart: {
    xData: ['18:00', '19:00', '20:00', '21:00', '22:00', '23:00', '00:00', '01:00'],
    yData: [120, 200, 150, 80, 70, 110, 130, 180]
  }
})

// 图表数据（初始为空）
const chartData = ref({
  problemChart: {
    categories: [],
    totalData: [],
    eventData: [],
    data: []
  },
  usageChart: {
    xData: [],
    yData: []
  }
})

// 图表配置参数
const posList = [
  'left',
  'right',
  'top',
  'bottom',
  'inside',
  'insideTop',
  'insideLeft',
  'insideRight',
  'insideBottom',
  'insideTopLeft',
  'insideTopRight',
  'insideBottomLeft',
  'insideBottomRight'
]

const configParameters = {
  rotate: {
    min: 1,
    max: 1
  },
  align: {
    options: {
      left: 'left',
      center: 'center',
      right: 'right'
    }
  },
  verticalAlign: {
    options: {
      top: 'top',
      middle: 'middle',
      bottom: 'bottom'
    }
  },
  position: {
    options: posList.reduce(function (map, pos) {
      map[pos] = pos;
      return map;
    }, {})
  },
  distance: {
    min: 0,
    max: 1
  }
}

const config = ref({
  rotate: 1,
  align: 'left',
  verticalAlign: 'middle',
  position: 'insideBottom',
  distance: 1
})

// 标签配置
const labelOption = computed(() => ({
  show: true,
  position: config.value.position,
  distance: config.value.distance,
  align: config.value.align,
  verticalAlign: config.value.verticalAlign,
  rotate: config.value.rotate,
  formatter: '{c}  {name|{a}}',
  fontSize: 1,
  rich: {
    name: {}
  }
}))

// 清空仪表盘数据函数
const clearDashboardData = () => {
  // 清空统计数据
  statsData.value = {
    subAccountCount: 0,
    storeCount: 0,
    todayNotifications: 0,
    problemCount: { current: 0, total: 0 }
  }

  // 清空TOP3数据
  topUsageData.value = []

  // 清空消息数据
  recentMessages.value = []

  // 清空图表数据
  chartData.value = {
    problemChart: {
      categories: [],
      totalData: [],
      eventData: [],
      data: []
    },
    usageChart: {
      xData: [],
      yData: []
    }
  }
}

// 图表实例
const problemChart = useCharts('main1')
const usageChart = useCharts('main2')

// 初始化图表
onMounted(() => {
//   window.dashboard_chartData = {
//   problemChart: {
//     categories: ['昨日', '今日'],
//     totalData: [1400, 1500],    // 新的总计数据
//     eventData: [1300, 1250],    // 新的事件名称数据
//     data: [
//       {
//         name: '昨日',
//         eventTotal: 400,
//         topEventName: '商品咨询',
//         topEventCount: 300
//       },
//       {
//         name: '今日',
//         eventTotal: 500,
//         topEventName: '物流查询',
//         topEventCount: 250
//       }
//     ]
//   },
//   usageChart: {
//     xData: ['18:00', '19:00', '20:00', '21:00', '22:00', '23:00', '00:00', '01:00'],
//     yData: [150, 220, 180, 90, 80, 120, 140, 200]
//   }
// };
// window.dashboard_recentMessages = [
//   {
//     store: '无忧',               // 店铺名称
//     user: '是是是',              // 用户名称
//     content: '我买的裤子质里很好...', // 消息内容
//     reply: '亲亲这款是标准长裤哦',    // 回复内容
//     time: '2025/6/17 17:36:33'   // 时间
//   },
//   {
//     store: '无忧',               // 店铺名称
//     user: '是是是',              // 用户名称
//     content: '我买的裤子质里很好...', // 消息内容
//     reply: '亲亲这款是标准长裤哦',    // 回复内容
//     time: '2025/6/17 17:36:33'   // 时间
//   },
//     {
//     store: '无忧',               // 店铺名称
//     user: '是是是',              // 用户名称
//     content: '我买的裤子质里很好...', // 消息内容
//     reply: '亲亲这款是标准长裤哦',    // 回复内容
//     time: '2025/6/17 17:36:33'   // 时间
//   },
//     {
//     store: '无忧',               // 店铺名称
//     user: '是是是',              // 用户名称
//     content: '我买的裤子质里很好...', // 消息内容
//     reply: '亲亲这款是标准长裤哦',    // 回复内容
//     time: '2025/6/17 17:36:33'   // 时间
//   },
//     {
//     store: '无忧',               // 店铺名称
//     user: '是是是',              // 用户名称
//     content: '我买的裤子质里很好...', // 消息内容
//     reply: '亲亲这款是标准长裤哦',    // 回复内容
//     time: '2025/6/17 17:36:33'   // 时间
//   },
//     {
//     store: '无忧',               // 店铺名称
//     user: '是是是',              // 用户名称
//     content: '我买的裤子质里很好...', // 消息内容
//     reply: '亲亲这款是标准长裤哦',    // 回复内容
//     time: '2025/6/17 17:36:33'   // 时间
//   },
//     {
//     store: '无忧',               // 店铺名称
//     user: '是是是',              // 用户名称
//     content: '我买的裤子质里很好...', // 消息内容
//     reply: '亲亲这款是标准长裤哦',    // 回复内容
//     time: '2025/6/17 17:36:33'   // 时间
//   },
//     {
//     store: '无忧',               // 店铺名称
//     user: '是是是',              // 用户名称
//     content: '我买的裤子质里很好...', // 消息内容
//     reply: '亲亲这款是标准长裤哦',    // 回复内容
//     time: '2025/6/17 17:36:33'   // 时间
//   },
//     {
//     store: '无忧',               // 店铺名称
//     user: '是是是',              // 用户名称
//     content: '我买的裤子质里很好...', // 消息内容
//     reply: '亲亲这款是标准长裤哦',    // 回复内容
//     time: '2025/6/17 17:36:33'   // 时间
//   },
//     {
//     store: '无忧',               // 店铺名称
//     user: '是是是',              // 用户名称
//     content: '我买的裤子质里很好...', // 消息内容
//     reply: '亲亲这款是标准长裤哦',    // 回复内容
//     time: '2025/6/17 17:36:33'   // 时间
//   },
//     {
//     store: '无忧',               // 店铺名称
//     user: '是是是',              // 用户名称
//     content: '我买的裤子质里很好...', // 消息内容
//     reply: '亲亲这款是标准长裤哦',    // 回复内容
//     time: '2025/6/17 17:36:33'   // 时间
//   },
// ]


// 调用更新函数
window.updateDashboardData();
  // 检查是否有全局数据，如果有就使用，没有就保持空白
  if (window.dashboard_stats || window.dashboard_topUsage || window.dashboard_recentMessages || window.dashboard_chartData) {
    // 有数据时从全局变量更新
    updateFromGlobalData()
  } else {
    // 没有数据时清空并初始化全局变量
    clearDashboardData()
    setupGlobalVariables()
  }

  // 问题分析横向柱状图 - 使用用户提供的配置
  const problemOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow' // 鼠标悬浮时的阴影指示器
      }
    },
    // 为了方便阅读，可以添加一个 legend 图例
    legend: {
      data: ['总计', '事件名称']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true // 防止标签溢出
    },
    // X 轴（现在是数值轴）
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01] // 坐标轴两边留白策略
    },
    // Y 轴（现在是类目轴）
    yAxis: {
      type: 'category',
      data: ['昨天', '今日'] // Y轴的分类
    },
    series: [
      {
        name: '总计',
        type: 'bar',
        data: chartData.value.problemChart.totalData,
        itemStyle: {
          color: 'rgb(69, 79, 204)'
        },
        label: {
          show: true,
          position: 'right', // 标签显示在条形图的右侧
          formatter: '{c}' // 只显示数值
        }
      },
      {
        name: '事件名称',
        type: 'bar',
        data: chartData.value.problemChart.eventData,
        itemStyle: {
          color: 'rgb(37, 172, 194)'
        },
        label: {
          show: true,
          position: 'right', // 标签显示在条形图的右侧
          formatter: '{c}' // 只显示数值
        }
      }
    ]
  }

  // 用量分析折线图配置
  const usageOption = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.value.usageChart.xData
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '用量',
        type: 'line',
        data: chartData.value.usageChart.yData,
        smooth: true,
        itemStyle: {
          color: '#7748f8'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(119, 72, 248, 0.3)' },
              { offset: 1, color: 'rgba(119, 72, 248, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  // 初始化图表
  problemChart.initChart(problemOption)
  usageChart.initChart(usageOption)

  // 初始化时设置全局变量
  setupGlobalVariables()
})

// 监听配置变化，动态更新图表
watch(config, () => {
  const updatedOption = {
    series: [
      {
        label: labelOption.value
      },
      {
        label: labelOption.value
      }
    ]
  }
  problemChart.updateChart(updatedOption)
}, { deep: true })

// 设置全局变量
const setupGlobalVariables = () => {
  // 设置仪表盘数据到全局变量
  window.dashboard_stats = statsData.value
  window.dashboard_topUsage = topUsageData.value
  window.dashboard_recentMessages = recentMessages.value
  window.dashboard_chartData = chartData.value
}

// 从全局变量更新数据
const updateFromGlobalData = () => {
  if (window.dashboard_stats) {
    statsData.value = window.dashboard_stats
  }
  if (window.dashboard_topUsage && Array.isArray(window.dashboard_topUsage)) {
    topUsageData.value = window.dashboard_topUsage
  }
  if (window.dashboard_recentMessages && Array.isArray(window.dashboard_recentMessages)) {
    recentMessages.value = window.dashboard_recentMessages
  }
  if (window.dashboard_chartData) {
    chartData.value = window.dashboard_chartData
    // 重新渲染图表
    updateCharts()
  }
}

// 更新图表
const updateCharts = () => {
  // 问题分析横向柱状图 - 使用动态数据
  const problemOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow' // 鼠标悬浮时的阴影指示器
      }
    },
    // 为了方便阅读，可以添加一个 legend 图例
    legend: {
      data: ['总计', '事件名称']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true // 防止标签溢出
    },
    // X 轴（现在是数值轴）
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01] // 坐标轴两边留白策略
    },
    // Y 轴（现在是类目轴）
    yAxis: {
      type: 'category',
      data: ['昨天', '今日'] // Y轴的分类
    },
    series: [
    {
      name: '总计',
      type: 'bar',
      data: chartData.value.problemChart?.totalData || [320, 332],
      itemStyle: {
        color: 'rgb(69, 79, 204)'
      },
      label: {
        show: true,
        position: 'right',
        formatter: '{c}'
      }
    },
    {
      name: '事件名称',
      type: 'bar',
      data: chartData.value.problemChart?.eventData || [220, 182],
      itemStyle: {
        color: 'rgb(37, 172, 194)'
      },
      label: {
        show: true,
        position: 'right',
        formatter: '{c}'
      }
    }
    ]
  }
  problemChart.initChart(problemOption)

  // 用量分析折线图
  const usageOption = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.value.usageChart.xData
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '用量',
        type: 'line',
        data: chartData.value.usageChart.yData,
        smooth: true,
        itemStyle: {
          color: '#7748f8'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(119, 72, 248, 0.3)' },
              { offset: 1, color: 'rgba(119, 72, 248, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  usageChart.initChart(usageOption)
}

// 监听数据变化并更新全局变量
watch([statsData, topUsageData, recentMessages, chartData], () => {
  setupGlobalVariables()
}, { deep: true })

// 暴露更新函数到全局
window.updateDashboardData = updateFromGlobalData
</script>

<style scoped>
/* 仪表盘整体容器尺寸限制 */
.body {
  max-width: 1000px;
  height: calc(100vh - 140px);
  min-height: 600px;
  margin: 0 auto;
  overflow: auto;
  box-sizing: border-box;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.body::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* 仪表盘卡片统一高度 */
.dashboard-card {
  height: 280px;
  display: flex;
  flex-direction: column;
}

.dashboard-card .layui-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 统计卡片区域特殊处理 */
.dashboard-card .layui-row.layui-col-space15 {
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
}

/* TOP3列表容器 */
.dashboard-card .ybp_list {
  flex: 1;
  overflow-y: auto;
  margin: 0;
  padding: 0;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.dashboard-card .ybp_list::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* 第二行卡片高度 */
.second-row-card {
  height: 320px;
  display: flex;
  flex-direction: column;
}

.second-row-card .layui-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 图表容器 */
.dashboard-card #main1 {
  flex: 1;
  min-height: 240px;
}

.second-row-card #main2 {
  flex: 1;
  min-height: 260px;
}

/* 统计卡片内边距优化 */
.dashboard-card .ybp_card {
  padding: 12px;
}

/* 最近消息表格容器 - 显示滚动条 */
.message-table-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 自定义滚动条样式 */
.message-table-container::-webkit-scrollbar {
  width: 8px;
}

.message-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.message-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.message-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保表格不会超出容器 */
.message-table-container .layui-table {
  margin-bottom: 0;
}
</style>
