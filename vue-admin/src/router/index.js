import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import('@/views/Dashboard.vue'),
      meta: {
        title: '仪表盘',
        icon: '&#xe6d2;'
      }
    },
    {
      path: '/sub-account',
      name: 'SubAccount',
      component: () => import('@/views/SubAccountManage.vue'),
      meta: {
        title: '子账号管理',
        icon: '&#xe612;'
      }
    },
    {
      path: '/store',
      name: 'Store',
      component: () => import('@/views/StoreManage.vue'),
      meta: {
        title: '店铺管理',
        icon: '&#xe6a2;'
      }
    },
    {
      path: '/work-order',
      name: 'WorkOrder',
      component: () => import('@/views/WorkOrderPanel.vue'),
      meta: {
        title: '对话预警',
        icon: '&#xe685;'
      }
    },
    {
      path: '/after-sales',
      name: 'AfterSales',
      component: () => import('@/views/AfterSalesOrder.vue'),
      meta: {
        title: '售后面板',
        icon: '&#xe685;'
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue'),
      meta: {
        title: '页面未找到'
      }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 智客AI`
  }
  next()
})

export default router
